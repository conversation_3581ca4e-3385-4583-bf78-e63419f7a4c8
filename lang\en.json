{"(and :count more error)": "(and :count more error)", "(and :count more errors)": "(and :count more error)|(and :count more errors)|(and :count more errors)", "A decryption key is required.": "A decryption key is required.", "A new verification link has been sent to the email address you provided in your profile settings.": "A new verification link has been sent to the email address you provided in your profile settings.", "A new verification link has been sent to your email address.": "A new verification link has been sent to your email address.", "A Timeout Occurred": "A Timeout Occurred", "Accept": "Accept", "Accept Invitation": "Accept Invitation", "Accepted": "Accepted", "Action": "Action", "Actions": "Actions", "Add": "Add", "Add :name": "Add :name", "Add a new team member to your team, allowing them to collaborate with you.": "Add a new team member to your team, allowing them to collaborate with you.", "Add additional security to your account using two factor authentication.": "Add additional security to your account using two factor authentication.", "Add Team Member": "Add Team Member", "Added.": "Added.", "Admin": "Admin", "Administrator": "Administrator", "Administrator users can perform any action.": "Administrator users can perform any action.", "Agree": "Agree", "All of the people that are part of this team.": "All of the people that are part of this team.", "All rights reserved.": "All rights reserved.", "Already registered?": "Already registered?", "Already Reported": "Already Reported", "API Token": "API Token", "API Token Permissions": "API Token Permissions", "API Tokens": "API Tokens", "API tokens allow third-party services to authenticate with our application on your behalf.": "API tokens allow third-party services to authenticate with our application on your behalf.", "Archive": "Archive", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.", "Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.": "Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.", "Are you sure you would like to delete this API token?": "Are you sure you would like to delete this API token?", "Are you sure you would like to leave this team?": "Are you sure you would like to leave this team?", "Are you sure you would like to remove this person from the team?": "Are you sure you would like to remove this person from the team?", "Assign": "Assign", "Associate": "Associate", "Attach": "Attach", "Bad Gateway": "Bad Gateway", "Bad Request": "Bad Request", "Bandwidth Limit Exceeded": "Bandwidth Limit Exceeded", "Before continuing, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.": "Before continuing, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.", "Browse": "Browse", "Browser Sessions": "Browser Sessions", "Cancel": "Cancel", "Choose": "<PERSON><PERSON>", "Choose :name": "Choose :name", "Choose File": "Choose <PERSON>", "Choose Image": "Choose Image", "Click here to re-send the verification email.": "Click here to re-send the verification email.", "Click to copy": "Click to copy", "Client Closed Request": "Client Closed Request", "Close": "Close", "Code": "Code", "Collapse": "Collapse", "Collapse All": "Collapse All", "Comment": "Comment", "Confirm": "Confirm", "Confirm Password": "Confirm Password", "Conflict": "Conflict", "Connect": "Connect", "Connection Closed Without Response": "Connection Closed Without Response", "Connection Timed Out": "Connection Timed Out", "Continue": "Continue", "Create": "Create", "Create :name": "Create :name", "Create a new team to collaborate with others on projects.": "Create a new team to collaborate with others on projects.", "Create Account": "Create Account", "Create API Token": "Create API Token", "Create New Team": "Create New Team", "Create Team": "Create Team", "Created": "Created", "Created.": "Created.", "Current Password": "Current Password", "Dashboard": "Dashboard", "Delete": "Delete", "Delete :name": "Delete :name", "Delete Account": "Delete Account", "Delete API Token": "Delete API Token", "Delete Team": "Delete Team", "Detach": "<PERSON><PERSON>", "Details": "Details", "Disable": "Disable", "Discard": "Discard", "Done": "Done", "Done.": "Done.", "Down": "Down", "Duplicate": "Duplicate", "Duplicate :name": "Duplicate :name", "Edit": "Edit", "Edit :name": "Edit :name", "Edit Profile": "Edit Profile", "Editor": "Editor", "Editor users have the ability to read, create, and update.": "Editor users have the ability to read, create, and update.", "Email": "Email", "Email Password Reset Link": "Email Password Reset Link", "Enable": "Enable", "Encrypted environment file already exists.": "Encrypted environment file already exists.", "Encrypted environment file not found.": "Encrypted environment file not found.", "Ensure your account is using a long, random password to stay secure.": "Ensure your account is using a long, random password to stay secure.", "Environment file already exists.": "Environment file already exists.", "Environment file not found.": "Environment file not found.", "errors": "errors", "Expand": "Expand", "Expand All": "Expand All", "Expectation Failed": "Expectation Failed", "Explanation": "Explanation", "Export": "Export", "Export :name": "Export :name", "Failed Dependency": "Failed Dependency", "File": "File", "Files": "Files", "Finish enabling two factor authentication.": "Finish enabling two factor authentication.", "For your security, please confirm your password to continue.": "For your security, please confirm your password to continue.", "Forbidden": "Forbidden", "Forgot your password?": "Forgot your password?", "Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.": "Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.", "Found": "Found", "Gateway Timeout": "Gateway Timeout", "Go Home": "Go Home", "Go to page :page": "Go to page :page", "Gone": "Gone", "Great! You have accepted the invitation to join the :team team.": "Great! You have accepted the invitation to join the :team team.", "Hello!": "Hello!", "Hide": "<PERSON>de", "Hide :name": "Hide :name", "Home": "Home", "HTTP Version Not Supported": "HTTP Version Not Supported", "I agree to the :terms_of_service and :privacy_policy": "I agree to the :terms_of_service and :privacy_policy", "I'm a teapot": "I'm a teapot", "If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.": "If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password.", "If you already have an account, you may accept this invitation by clicking the button below:": "If you already have an account, you may accept this invitation by clicking the button below:", "If you did not create an account, no further action is required.": "If you did not create an account, no further action is required.", "If you did not expect to receive an invitation to this team, you may discard this email.": "If you did not expect to receive an invitation to this team, you may discard this email.", "If you did not request a password reset, no further action is required.": "If you did not request a password reset, no further action is required.", "If you do not have an account, you may create one by clicking the button below. After creating an account, you may click the invitation acceptance button in this email to accept the team invitation:": "If you do not have an account, you may create one by clicking the button below. After creating an account, you may click the invitation acceptance button in this email to accept the team invitation:", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:", "IM Used": "IM Used", "Image": "Image", "Impersonate": "Impersonate", "Impersonation": "Impersonation", "Import": "Import", "Import :name": "Import :name", "Insufficient Storage": "Insufficient Storage", "Internal Server Error": "Internal Server Error", "Introduction": "Introduction", "Invalid filename.": "Invalid filename.", "Invalid JSON was returned from the route.": "Invalid JSON was returned from the route.", "Invalid SSL Certificate": "Invalid SSL Certificate", "Last active": "Last active", "Last used": "Last used", "Leave": "Leave", "Leave Team": "Leave Team", "length": "length", "Length Required": "Length Required", "Like": "Like", "Load": "Load", "Localize": "Localize", "Location": "Location", "Locked": "Locked", "Log In": "Log In", "Log in": "Log in", "Log Out": "Log Out", "Log Out Other Browser Sessions": "Log Out Other Browser Sessions", "Login": "<PERSON><PERSON>", "Logout": "Logout", "Loop Detected": "Loop Detected", "Maintenance Mode": "Maintenance Mode", "Manage Account": "Manage Account", "Manage and log out your active sessions on other browsers and devices.": "Manage and log out your active sessions on other browsers and devices.", "Manage API Tokens": "Manage API Tokens", "Manage Role": "Manage Role", "Manage Team": "Manage Team", "Method Not Allowed": "Method Not Allowed", "Misdirected Request": "Misdirected Request", "Moved Permanently": "Moved Permanently", "Multi-Status": "Multi-Status", "Multiple Choices": "Multiple Choices", "Name": "Name", "Network Authentication Required": "Network Authentication Required", "Network Connect Timeout Error": "Network Connect Timeout Error", "Network Read Timeout Error": "Network Read Timeout Error", "New": "New", "New :name": "New :name", "New Password": "New Password", "No": "No", "No Content": "No Content", "Non-Authoritative Information": "Non-Authoritative Information", "Not Acceptable": "Not Acceptable", "Not Extended": "Not Extended", "Not Found": "Not Found", "Not Implemented": "Not Implemented", "Not Modified": "Not Modified", "of": "of", "OK": "OK", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.", "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.": "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.", "Open": "Open", "Open in a current window": "Open in a current window", "Open in a new window": "Open in a new window", "Open in a parent frame": "Open in a parent frame", "Open in the topmost frame": "Open in the topmost frame", "Open on the website": "Open on the website", "Origin Is Unreachable": "Origin Is Unreachable", "Page Expired": "Page Expired", "Pagination Navigation": "Pagination Navigation", "Partial Content": "Partial Content", "Password": "Password", "Payload Too Large": "Payload Too Large", "Payment Required": "Payment Required", "Pending Team Invitations": "Pending Team Invitations", "Permanent Redirect": "Permanent Redirect", "Permanently delete this team.": "Permanently delete this team.", "Permanently delete your account.": "Permanently delete your account.", "Permissions": "Permissions", "Photo": "Photo", "Please click the button below to verify your email address.": "Please click the button below to verify your email address.", "Please confirm access to your account by entering one of your emergency recovery codes.": "Please confirm access to your account by entering one of your emergency recovery codes.", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "Please confirm access to your account by entering the authentication code provided by your authenticator application.", "Please copy your new API token. For your security, it won't be shown again.": "Please copy your new API token. For your security, it won't be shown again.", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.", "Please provide the email address of the person you would like to add to this team.": "Please provide the email address of the person you would like to add to this team.", "Precondition Failed": "Precondition Failed", "Precondition Required": "Precondition Required", "Preview": "Preview", "Price": "Price", "Privacy Policy": "Privacy Policy", "Processing": "Processing", "Profile": "Profile", "Profile Information": "Profile Information", "Proxy Authentication Required": "Proxy Authentication Required", "Railgun Error": "Railgun Error", "Range Not Satisfiable": "Range Not Satisfiable", "Record": "Record", "Recovery Code": "Recovery Code", "Regards,": "<PERSON><PERSON>,", "Regenerate Recovery Codes": "Regenerate Recovery Codes", "Register": "Register", "Remember me": "Remember me", "Remove": "Remove", "Remove Photo": "Remove Photo", "Remove Team Member": "Remove Team Member", "Request Header Fields Too Large": "Request Header Fields Too Large", "Request Timeout": "Request Timeout", "Resend Verification Email": "Resend Verification Email", "Reset Content": "Reset Content", "Reset Password": "Reset Password", "Reset Password Notification": "Reset Password Notification", "Restore": "Rest<PERSON>", "Restore :name": "Restore :name", "results": "results", "Retry With": "Retry With", "Role": "Role", "Save": "Save", "Save & Close": "Save & Close", "Save & Return": "Save & Return", "Save :name": "Save :name", "Saved.": "Saved.", "Search": "Search", "Search :name": "Search :name", "See Other": "See Other", "Select": "Select", "Select A New Photo": "Select A New Photo", "Select All": "Select All", "Send": "Send", "Server Error": "Server Error", "Service Unavailable": "Service Unavailable", "Session Has Expired": "Session Has Expired", "Settings": "Settings", "Setup Key": "Setup Key", "Show": "Show", "Show :name": "Show :name", "Show All": "Show All", "Show Recovery Codes": "Show Recovery Codes", "Showing": "Showing", "Sign In": "Sign In", "Solve": "Solve", "SSL Handshake Failed": "SSL Handshake Failed", "Start": "Start", "Stop": "Stop", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.", "Submit": "Submit", "Subscribe": "Subscribe", "Switch": "Switch", "Switch Teams": "Switch Teams", "Switch To Role": "Switch To Role", "Switching Protocols": "Switching Protocols", "Tag": "Tag", "Tags": "Tags", "Team Details": "Team Details", "Team Invitation": "Team Invitation", "Team Members": "Team Members", "Team Name": "Team Name", "Team Owner": "Team Owner", "Team Settings": "Team Settings", "Temporary Redirect": "Temporary Redirect", "Terms of Service": "Terms of Service", "The :attribute must be a valid role.": "The :attribute must be a valid role.", "The :attribute must be at least :length characters and contain at least one number.": "The :attribute must be at least :length characters and contain at least one number.", "The :attribute must be at least :length characters and contain at least one special character and one number.": "The :attribute must be at least :length characters and contain at least one special character and one number.", "The :attribute must be at least :length characters and contain at least one special character.": "The :attribute must be at least :length characters and contain at least one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "The :attribute must be at least :length characters and contain at least one uppercase character and one number.", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character.": "The :attribute must be at least :length characters and contain at least one uppercase character.", "The :attribute must be at least :length characters.": "The :attribute must be at least :length characters.", "The given data was invalid.": "The given data was invalid.", "The password is incorrect.": "The password is incorrect.", "The provided password does not match your current password.": "The provided password does not match your current password.", "The provided password was incorrect.": "The provided password was incorrect.", "The provided two factor authentication code was invalid.": "The provided two factor authentication code was invalid.", "The provided two factor recovery code was invalid.": "The provided two factor recovery code was invalid.", "The response is not a streamed response.": "The response is not a streamed response.", "The response is not a view.": "The response is not a view.", "The team's name and owner information.": "The team's name and owner information.", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.", "This action is unauthorized.": "This action is unauthorized.", "This device": "This device", "This is a secure area of the application. Please confirm your password before continuing.": "This is a secure area of the application. Please confirm your password before continuing.", "This password does not match our records.": "This password does not match our records.", "This password reset link will expire in :count minutes.": "This password reset link will expire in :count minutes.", "This user already belongs to the team.": "This user already belongs to the team.", "This user has already been invited to the team.": "This user has already been invited to the team.", "to": "to", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code.": "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code.", "Toggle navigation": "Toggle navigation", "Token Name": "Token Name", "Too Early": "Too Early", "Too Many Requests": "Too Many Requests", "Translate": "Translate", "Translate It": "Translate It", "Two Factor Authentication": "Two Factor Authentication", "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application or enter the setup key.": "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application or enter the setup key.", "Unauthorized": "Unauthorized", "Unavailable For Legal Reasons": "Unavailable For Legal Reasons", "Unknown": "Unknown", "Unknown Error": "Unknown Error", "Unpack": "Unpack", "Unprocessable Entity": "Unprocessable Entity", "Unsubscribe": "Unsubscribe", "Unsupported Media Type": "Unsupported Media Type", "Up": "Up", "Update": "Update", "Update :name": "Update :name", "Update Password": "Update Password", "Update your account's profile information and email address.": "Update your account's profile information and email address.", "Upgrade Required": "Upgrade Required", "URI Too Long": "URI Too Long", "Use a recovery code": "Use a recovery code", "Use an authentication code": "Use an authentication code", "Use Proxy": "Use Proxy", "User": "User", "Variant Also Negotiates": "Variant Also Negotiates", "Verify Email Address": "Verify Em<PERSON> Address", "View": "View", "View :name": "View :name", "We were unable to find a registered user with this email address.": "We were unable to find a registered user with this email address.", "Web Server is Down": "Web Server is Down", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.", "Whoops!": "Whoops!", "Whoops! Something went wrong.": "Whoops! Something went wrong.", "Yes": "Yes", "You are receiving this email because we received a password reset request for your account.": "You are receiving this email because we received a password reset request for your account.", "You have been invited to join the :team team!": "You have been invited to join the :team team!", "You have enabled two factor authentication.": "You have enabled two factor authentication.", "You have not enabled two factor authentication.": "You have not enabled two factor authentication.", "You may accept this invitation by clicking the button below:": "You may accept this invitation by clicking the button below:", "You may delete any of your existing tokens if they are no longer needed.": "You may delete any of your existing tokens if they are no longer needed.", "You may not delete your personal team.": "You may not delete your personal team.", "You may not leave a team that you created.": "You may not leave a team that you created.", "Your email address is unverified.": "Your email address is unverified."}